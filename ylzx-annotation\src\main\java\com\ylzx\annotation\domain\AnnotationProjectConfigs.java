package com.ylzx.annotation.domain;

import java.io.Serial;

import com.ylzx.common.annotation.Excel;
import com.ylzx.common.core.domain.BaseEntity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 标注项目配置对象 annotation_project_configs
 * 
 * <AUTHOR>
 * @date 2025-06-19
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class AnnotationProjectConfigs extends BaseEntity
{
    @Serial
    private static final long serialVersionUID = 1L;

    /** 标注项目配置主键 */
    private Long configId;

    /** 标注项目主键 */
    @Excel(name = "标注项目主键")
    private String projectId;

    /** 宽度 */
    @Excel(name = "宽度")
    private Long width;

    /** 高度 */
    @Excel(name = "高度")
    private Long height;

    /** 转换 */
    @Excel(name = "转换")
    private String transformations;
}
