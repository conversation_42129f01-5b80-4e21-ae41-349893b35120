package com.ylzx.annotation.service.impl;

import java.util.List;
import java.util.Objects;
import java.util.Random;
import java.math.BigDecimal;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ylzx.annotation.domain.AnnotationCategoriesImages;
import com.ylzx.annotation.mapper.AnnotationCategoriesImagesMapper;
import com.ylzx.annotation.domain.AnnotationCategories;
import com.ylzx.annotation.service.AnnotationCategoriesService;
import com.ylzx.annotation.domain.AnnotationReviews;
import com.ylzx.annotation.service.AnnotationReviewsService;
import com.ylzx.annotation.domain.enums.AnnotationStatus;
import com.ylzx.annotation.utils.AnnotatorHelper;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import com.ylzx.annotation.mapper.AnnotationAnnotationsMapper;
import com.ylzx.annotation.domain.AnnotationAnnotations;
import com.ylzx.annotation.service.AnnotationAnnotationsService;

/**
 * 标注Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-19
 */
@Service
public class AnnotationAnnotationsServiceImpl extends ServiceImpl<AnnotationAnnotationsMapper,AnnotationAnnotations> implements AnnotationAnnotationsService
{
    @Resource
    private AnnotationAnnotationsMapper annotationAnnotationsMapper;

    @Resource
    private AnnotationCategoriesImagesMapper annotationCategoriesImagesMapper;

    @Autowired
    private AnnotationCategoriesService annotationCategoriesService;

    @Lazy
    @Autowired
    private AnnotationReviewsService annotationReviewsService;

    @Autowired
    private AnnotatorHelper annotatorHelper;

    /**
     * 查询标注
     * 
     * @param annotationId 标注主键
     * @return 标注
     */
    @Override
    public AnnotationAnnotations selectAnnotationAnnotationsByAnnotationId(Long annotationId)
    {
        return annotationAnnotationsMapper.selectAnnotationAnnotationsByAnnotationId(annotationId);
    }

    /**
     * 查询标注列表
     * 
     * @param annotationAnnotations 标注
     * @return 标注集合
     */
    @Override
    public List<AnnotationAnnotations> selectAnnotationAnnotationsList(AnnotationAnnotations annotationAnnotations)
    {
        return annotationAnnotationsMapper.selectAnnotationAnnotationsList(annotationAnnotations);
    }

    /**
     * 新增标注
     *
     * @param annotationAnnotations 标注
     * @return 结果
     */
    @Override
    public int insertAnnotationAnnotations(AnnotationAnnotations annotationAnnotations)
    {
        int result = annotationAnnotationsMapper.insertAnnotationAnnotations(annotationAnnotations);

        // 如果标注状态为待审核，则根据分类的审核比例自动生成审核记录
        if (result > 0 && AnnotationStatus.PENDING_REVIEW.equals(annotationAnnotations.getStatus())) {
            autoGenerateReviewRecord(annotationAnnotations);
        }

        return result;
    }

    /**
     * 自动生成审核记录（按概率）
     *
     * @param annotation 标注记录
     */
    private void autoGenerateReviewRecord(AnnotationAnnotations annotation) {
        try {
            // 1. 获取分类的审核比例
            AnnotationCategories category = annotationCategoriesService.selectAnnotationCategoriesByCategoryId(annotation.getCategoryId());
            if (category == null || category.getReviewRatio() == null || category.getReviewRatio().compareTo(BigDecimal.ZERO) <= 0) {
                return; // 没有设置审核比例，不进行抽审
            }

            // 2. 检查是否已存在审核记录
            if (annotationReviewsService.existsReviewByAnnotationId(annotation.getAnnotationId())) {
                return; // 已存在审核记录，不重复生成
            }

            // 3. 按概率决定是否生成审核记录
            Random random = new Random();
            double randomValue = random.nextDouble();
            if (randomValue <= category.getReviewRatio().doubleValue()) {
                // 4. 获取标注人ID
                Long annotatorId = annotatorHelper.findAnnotatorId(annotation.getImageId(), annotation.getCategoryId());

                // 5. 创建审核记录
                AnnotationReviews review = AnnotationReviews.builder()
                    .annotationId(annotation.getAnnotationId())
                    .annotatorId(annotatorId)
                    .imageId(annotation.getImageId())
                    .status(AnnotationStatus.PENDING_REVIEW) // 待审核状态
                    .build();

                annotationReviewsService.insertAnnotationReviews(review);
            }
        } catch (Exception e) {
            // 记录日志但不影响主流程
            System.err.println("自动生成审核记录失败: " + e.getMessage());
        }
    }

    /**
     * 修改标注
     * 
     * @param annotationAnnotations 标注
     * @return 结果
     */
    @Override
    public int updateAnnotationAnnotations(AnnotationAnnotations annotationAnnotations)
    {
        return annotationAnnotationsMapper.updateAnnotationAnnotations(annotationAnnotations);
    }

    /**
     * 修改标注状态
     * 
     * @param annotationAnnotations 标注
     * @return 结果
     */
    @Override
    public int updateAnnotationAnnotationsStatus(AnnotationAnnotations annotationAnnotations)
    {
        int rows = annotationAnnotationsMapper.updateAnnotationAnnotationsStatus(annotationAnnotations);
        if (rows > 0) {
            AnnotationAnnotations overallStatusResult = this.checkOverallStatusByAnnotationId(annotationAnnotations.getAnnotationId());
            if (Objects.nonNull(overallStatusResult) && Objects.equals(annotationAnnotations.getStatus(), overallStatusResult.getStatus())) {
                AnnotationCategoriesImages imageCategory = new AnnotationCategoriesImages();
                imageCategory.setCategoryId(overallStatusResult.getCategoryId());
                imageCategory.setImageId(overallStatusResult.getImageId());
                imageCategory.setStatus(overallStatusResult.getStatus());
                annotationCategoriesImagesMapper.updateAnnotationCategoriesImages(imageCategory);
            }
        }
        return rows;
    }

    /**
     * 批量逻辑删除标注
     * 
     * @param annotationIds 需要删除的标注主键
     * @return 结果
     */
    @Override
    public int deleteAnnotationAnnotationsByAnnotationIds(Long[] annotationIds)
    {
        return annotationAnnotationsMapper.logicDeleteAnnotationAnnotationsByAnnotationIds(annotationIds);
    }

    /**
     * 逻辑删除标注信息
     * 
     * @param annotationId 标注主键
     * @return 结果
     */
    @Override
    public int deleteAnnotationAnnotationsByAnnotationId(Long annotationId)
    {
        return annotationAnnotationsMapper.logicDeleteAnnotationAnnotationsByAnnotationId(annotationId);
    }

    /**
     * 【SQL优化】根据单个标注ID，一次性查询同一个图片和类别下的所有标注的整体状态
     *
     * @param annotationId 标注ID
     * @return 整体状态 ('1': 未审核完, '2': 审核通过, '3': 审核不通过)
     */
    @Override
    public AnnotationAnnotations checkOverallStatusByAnnotationId(Long annotationId) {
        return annotationAnnotationsMapper.checkOverallStatusInSql(annotationId);
    }
}
