package com.ylzx.annotation.service.impl;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ylzx.annotation.mapper.AnnotationProjectConfigsMapper;
import com.ylzx.annotation.domain.AnnotationProjectConfigs;
import com.ylzx.annotation.service.AnnotationProjectConfigsService;

/**
 * 标注项目配置Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-19
 */
@Service
public class AnnotationProjectConfigsServiceImpl extends ServiceImpl<AnnotationProjectConfigsMapper,AnnotationProjectConfigs> implements AnnotationProjectConfigsService
{
    @Autowired
    private AnnotationProjectConfigsMapper annotationProjectConfigsMapper;

    /**
     * 查询标注项目配置
     * 
     * @param configId 标注项目配置主键
     * @return 标注项目配置
     */
    @Override
    public AnnotationProjectConfigs selectAnnotationProjectConfigsByConfigId(Long configId)
    {
        return annotationProjectConfigsMapper.selectAnnotationProjectConfigsByConfigId(configId);
    }

    /**
     * 查询标注项目配置列表
     * 
     * @param annotationProjectConfigs 标注项目配置
     * @return 标注项目配置集合
     */
    @Override
    public List<AnnotationProjectConfigs> selectAnnotationProjectConfigsList(AnnotationProjectConfigs annotationProjectConfigs)
    {
        return annotationProjectConfigsMapper.selectAnnotationProjectConfigsList(annotationProjectConfigs);
    }

    /**
     * 新增标注项目配置
     * 
     * @param annotationProjectConfigs 标注项目配置
     * @return 结果
     */
    @Override
    public int insertAnnotationProjectConfigs(AnnotationProjectConfigs annotationProjectConfigs)
    {
        return annotationProjectConfigsMapper.insertAnnotationProjectConfigs(annotationProjectConfigs);
    }

    /**
     * 修改标注项目配置
     * 
     * @param annotationProjectConfigs 标注项目配置
     * @return 结果
     */
    @Override
    public int updateAnnotationProjectConfigs(AnnotationProjectConfigs annotationProjectConfigs)
    {
        return annotationProjectConfigsMapper.updateAnnotationProjectConfigs(annotationProjectConfigs);
    }

    /**
     * 批量删除标注项目配置
     * 
     * @param configIds 需要删除的标注项目配置主键
     * @return 结果
     */
    @Override
    public int deleteAnnotationProjectConfigsByConfigIds(Long[] configIds)
    {
        return annotationProjectConfigsMapper.deleteAnnotationProjectConfigsByConfigIds(configIds);
    }

    /**
     * 删除标注项目配置信息
     * 
     * @param configId 标注项目配置主键
     * @return 结果
     */
    @Override
    public int deleteAnnotationProjectConfigsByConfigId(Long configId)
    {
        return annotationProjectConfigsMapper.deleteAnnotationProjectConfigsByConfigId(configId);
    }
}
