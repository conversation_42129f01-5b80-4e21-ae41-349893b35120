package com.ylzx.annotation.service.impl;

import java.util.List;
import java.util.Map;
import java.util.ArrayList;
import java.util.Collections;
import java.util.stream.Collectors;
import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ylzx.annotation.mapper.AnnotationReviewsMapper;
import com.ylzx.annotation.domain.AnnotationReviews;
import com.ylzx.annotation.domain.AnnotationImages;
import com.ylzx.annotation.domain.vo.ReviewTaskWithDetailsVo;
import com.ylzx.annotation.service.AnnotationReviewsService;
import com.ylzx.annotation.domain.AnnotationAnnotations;
import com.ylzx.annotation.service.AnnotationAnnotationsService;
import com.ylzx.annotation.service.AnnotationImagesService;

import com.ylzx.annotation.service.AnnotationCategoriesImagesService;
import com.ylzx.annotation.domain.AnnotationCategories;
import com.ylzx.annotation.service.AnnotationCategoriesService;
import com.ylzx.annotation.domain.enums.AnnotationStatus;
import com.ylzx.common.utils.DateUtils;
import com.ylzx.common.utils.SecurityUtils;
import org.springframework.transaction.annotation.Transactional;

/**
 * 标注审核Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-19
 */
@Service
public class AnnotationReviewsServiceImpl extends ServiceImpl<AnnotationReviewsMapper,AnnotationReviews> implements AnnotationReviewsService
{
    @Autowired
    private AnnotationReviewsMapper annotationReviewsMapper;

    @Autowired
    private AnnotationAnnotationsService annotationAnnotationsService;

    @Autowired
    private AnnotationCategoriesImagesService annotationCategoriesImagesService;

    @Autowired
    private AnnotationCategoriesService annotationCategoriesService;

    @Autowired
    private AnnotationImagesService annotationImagesService;

    /**
     * 查询标注审核
     * 
     * @param reviewId 标注审核主键
     * @return 标注审核
     */
    @Override
    public AnnotationReviews selectAnnotationReviewsByReviewId(Long reviewId)
    {
        return annotationReviewsMapper.selectAnnotationReviewsByReviewId(reviewId);
    }

    /**
     * 查询标注审核列表
     * 
     * @param annotationReviews 标注审核
     * @return 标注审核集合
     */
    @Override
    public List<AnnotationReviews> selectAnnotationReviewsList(AnnotationReviews annotationReviews)
    {
        return annotationReviewsMapper.selectAnnotationReviewsList(annotationReviews);
    }

    /**
     * 新增标注审核
     *
     * @param annotationReviews 标注审核
     * @return 结果
     */
    @Override
    @Transactional
    public int insertAnnotationReviews(AnnotationReviews annotationReviews)
    {
        // 1. 获取标注信息以获取imageId
        AnnotationAnnotations annotation = annotationAnnotationsService.selectAnnotationAnnotationsByAnnotationId(annotationReviews.getAnnotationId());
        if (annotation != null) {
            // 自动填充imageId
            annotationReviews.setImageId(annotation.getImageId());

            // 2. 通过imageId和categoryId查找标注人ID
            Long annotatorId = annotationCategoriesImagesService.findAnnotatorId(annotation.getImageId(), annotation.getCategoryId());
            if (annotatorId != null) {
                annotationReviews.setAnnotatorId(annotatorId);
            }
        }

        // 3. 更新标注状态
        AnnotationAnnotations annotationAnnotations = new AnnotationAnnotations();
        annotationAnnotations.setAnnotationId(annotationReviews.getAnnotationId());
        annotationAnnotations.setStatus(annotationReviews.getStatus());
        annotationAnnotationsService.updateAnnotationAnnotationsStatus(annotationAnnotations);

        // 4. 设置审核人和审核时间
        annotationReviews.setAuditor(SecurityUtils.getUsername());
        annotationReviews.setAuditTime(DateUtils.getNowLocalDateTime());

        return annotationReviewsMapper.insertAnnotationReviews(annotationReviews);
    }

    /**
     * 修改标注审核
     * 
     * @param annotationReviews 标注审核
     * @return 结果
     */
    @Override
    public int updateAnnotationReviews(AnnotationReviews annotationReviews)
    {
        return annotationReviewsMapper.updateAnnotationReviews(annotationReviews);
    }

    /**
     * 批量删除标注审核
     * 
     * @param reviewIds 需要删除的标注审核主键
     * @return 结果
     */
    @Override
    public int deleteAnnotationReviewsByReviewIds(Long[] reviewIds)
    {
        return annotationReviewsMapper.deleteAnnotationReviewsByReviewIds(reviewIds);
    }

    /**
     * 删除标注审核信息
     *
     * @param reviewId 标注审核主键
     * @return 结果
     */
    @Override
    public int deleteAnnotationReviewsByReviewId(Long reviewId)
    {
        return annotationReviewsMapper.deleteAnnotationReviewsByReviewId(reviewId);
    }

    /**
     * 根据标注人ID查询审核记录
     *
     * @param annotatorId 标注人ID
     * @return 审核记录列表
     */
    @Override
    public List<AnnotationReviews> selectAnnotationReviewsByAnnotatorId(Long annotatorId)
    {
        return annotationReviewsMapper.selectAnnotationReviewsByAnnotatorId(annotatorId);
    }

    /**
     * 根据图片ID查询审核记录
     *
     * @param imageId 图片ID
     * @return 审核记录列表
     */
    @Override
    public List<AnnotationReviews> selectAnnotationReviewsByImageId(Long imageId)
    {
        return annotationReviewsMapper.selectAnnotationReviewsByImageId(imageId);
    }

    /**
     * 根据标注人ID和状态查询审核记录
     *
     * @param annotatorId 标注人ID
     * @param status 审核状态
     * @return 审核记录列表
     */
    @Override
    public List<AnnotationReviews> selectAnnotationReviewsByAnnotatorIdAndStatus(Long annotatorId, com.ylzx.annotation.domain.enums.AnnotationStatus status)
    {
        return annotationReviewsMapper.selectAnnotationReviewsByAnnotatorIdAndStatus(annotatorId, status);
    }

    /**
     * 统计标注人的审核情况
     *
     * @param annotatorId 标注人ID
     * @return 统计结果 Map，包含各种状态的数量和比例
     */
    @Override
    public Map<String, Object> countReviewStatsByAnnotatorId(Long annotatorId)
    {
        return annotationReviewsMapper.countReviewStatsByAnnotatorId(annotatorId);
    }

    /**
     * 生成抽审记录
     *
     * @param categoryId 标注分类ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param reviewRatio 抽审比例 (0-1之间的小数，如0.1表示10%)
     * @param reviewCount 抽审数量 (如果指定数量，则忽略比例)
     * @return 生成的抽审记录数量
     */
    @Override
    @Transactional
    public int generateReviewRecords(Long categoryId, LocalDateTime startTime,
                                   LocalDateTime endTime, BigDecimal reviewRatio,
                                   Integer reviewCount) {
        // 1. 查询符合条件的标注记录
        List<AnnotationAnnotations> annotations = annotationReviewsMapper.selectAnnotationsForReview(
            categoryId, startTime, endTime, null);

        if (annotations.isEmpty()) {
            return 0;
        }

        // 2. 过滤掉已经存在审核记录的标注
        List<AnnotationAnnotations> filteredAnnotations = annotations.stream()
            .filter(annotation -> !existsReviewByAnnotationId(annotation.getAnnotationId()))
            .collect(Collectors.toList());

        if (filteredAnnotations.isEmpty()) {
            return 0;
        }

        // 3. 确定抽审数量
        int targetCount;
        if (reviewCount != null && reviewCount > 0) {
            // 使用指定数量
            targetCount = Math.min(reviewCount, filteredAnnotations.size());
        } else if (reviewRatio != null && reviewRatio.compareTo(BigDecimal.ZERO) > 0) {
            // 使用比例计算
            targetCount = Math.max(1, reviewRatio.multiply(new BigDecimal(filteredAnnotations.size())).intValue());
        } else {
            // 如果没有指定比例和数量，使用分类配置的审核比例
            if (categoryId != null) {
                AnnotationCategories category = annotationCategoriesService.selectAnnotationCategoriesByCategoryId(categoryId);
                if (category != null && category.getReviewRatio() != null) {
                    targetCount = Math.max(1, category.getReviewRatio().multiply(new BigDecimal(filteredAnnotations.size())).intValue());
                } else {
                    targetCount = Math.max(1, filteredAnnotations.size() / 10); // 默认10%
                }
            } else {
                targetCount = Math.max(1, filteredAnnotations.size() / 10); // 默认10%
            }
        }

        // 4. 随机选择标注记录
        Collections.shuffle(filteredAnnotations);
        List<AnnotationAnnotations> selectedAnnotations = filteredAnnotations.stream()
            .limit(targetCount)
            .collect(Collectors.toList());

        // 5. 创建审核记录
        List<AnnotationReviews> reviewList = new ArrayList<>();
        for (AnnotationAnnotations annotation : selectedAnnotations) {
            // 通过imageId和categoryId查找标注人ID
            Long annotatorId = annotationCategoriesImagesService.findAnnotatorId(annotation.getImageId(), annotation.getCategoryId());

            AnnotationReviews review = AnnotationReviews.builder()
                .annotationId(annotation.getAnnotationId())
                .annotatorId(annotatorId)
                .imageId(annotation.getImageId())
                .status(AnnotationStatus.PENDING_REVIEW) // 待审核状态
                .build();
            reviewList.add(review);
        }

        // 6. 批量插入审核记录
        if (!reviewList.isEmpty()) {
            return annotationReviewsMapper.batchInsertReviews(reviewList);
        }

        return 0;
    }

    /**
     * 领取审核任务
     *
     * @param categoryId 标注分类ID (可选，为null时领取所有分类的任务)
     * @param limit 领取数量
     * @return 领取到的审核任务列表
     */
    @Override
    @Transactional
    public List<AnnotationReviews> claimReviewTasks(Long categoryId, int limit) {
        String username = SecurityUtils.getUsername();

        // 1. 查询可领取的审核任务
        List<AnnotationReviews> availableTasks = annotationReviewsMapper.selectAvailableReviewTasks(
            categoryId, username, limit);

        if (availableTasks.isEmpty()) {
            return Collections.emptyList();
        }

        // 2. 获取任务ID列表
        List<Long> reviewIds = availableTasks.stream()
            .map(AnnotationReviews::getReviewId)
            .collect(Collectors.toList());

        // 3. 批量更新审核员和领取时间
        LocalDateTime claimTime = LocalDateTime.now();
        int updatedCount = annotationReviewsMapper.batchUpdateReviewer(reviewIds, username, claimTime);

        if (updatedCount > 0) {
            // 4. 更新返回的任务对象
            availableTasks.forEach(task -> {
                task.setAuditor(username);
                task.setAuditTime(claimTime);
            });
            return availableTasks;
        }

        return Collections.emptyList();
    }

    /**
     * 检查标注记录是否已存在审核记录
     *
     * @param annotationId 标注ID
     * @return 是否存在
     */
    @Override
    public boolean existsReviewByAnnotationId(Long annotationId) {
        return annotationReviewsMapper.countByAnnotationId(annotationId) > 0;
    }

    /**
     * 领取审核任务（返回详细信息，包含图片和标注）
     *
     * @param categoryId 标注分类ID (可选，为null时领取所有分类的任务)
     * @param limit 领取数量
     * @return 领取到的审核任务详细信息列表
     */
    @Override
    @Transactional
    public List<ReviewTaskWithDetailsVo> claimReviewTasksWithDetails(Long categoryId, int limit) {
        // 先领取审核任务
        List<AnnotationReviews> reviewTasks = claimReviewTasks(categoryId, limit);

        if (reviewTasks == null || reviewTasks.isEmpty()) {
            return Collections.emptyList();
        }

        // 转换为详细信息
        return reviewTasks.stream().map(this::convertToReviewTaskWithDetails).collect(Collectors.toList());
    }

    /**
     * 获取我的审核任务列表（返回详细信息，包含图片和标注）
     *
     * @param categoryId 标注分类ID (可选，为null时查询所有分类的任务)
     * @param statuses 审核状态列表 (可选)
     * @return 审核任务详细信息列表
     */
    @Override
    public List<ReviewTaskWithDetailsVo> getMyReviewTasksWithDetails(Long categoryId, List<AnnotationStatus> statuses) {
        String username = SecurityUtils.getUsername();

        // 构建查询条件
        AnnotationReviews queryReview = new AnnotationReviews();
        queryReview.setAuditor(username);

        // 查询我的审核任务
        List<AnnotationReviews> reviewTasks = selectAnnotationReviewsList(queryReview);

        if (reviewTasks == null || reviewTasks.isEmpty()) {
            return Collections.emptyList();
        }

        // 过滤条件
        List<AnnotationReviews> filteredTasks = reviewTasks.stream()
                .filter(task -> categoryId == null || categoryId.equals(getCategoryIdFromTask(task)))
                .filter(task -> statuses == null || statuses.isEmpty() || statuses.contains(task.getStatus()))
                .collect(Collectors.toList());

        // 转换为详细信息
        return filteredTasks.stream().map(this::convertToReviewTaskWithDetails).collect(Collectors.toList());
    }

    /**
     * 获取单个审核任务的详细信息（包含图片和标注）
     *
     * @param reviewId 审核任务ID
     * @return 审核任务详细信息
     */
    @Override
    public ReviewTaskWithDetailsVo getReviewTaskWithDetails(Long reviewId) {
        AnnotationReviews reviewTask = selectAnnotationReviewsByReviewId(reviewId);
        if (reviewTask == null) {
            return null;
        }

        return convertToReviewTaskWithDetails(reviewTask);
    }

    /**
     * 将AnnotationReviews转换为ReviewTaskWithDetailsVo
     */
    private ReviewTaskWithDetailsVo convertToReviewTaskWithDetails(AnnotationReviews reviewTask) {
        // 获取当前审核的标注信息
        AnnotationAnnotations currentAnnotation = annotationAnnotationsService.selectAnnotationAnnotationsByAnnotationId(reviewTask.getAnnotationId());
        if (currentAnnotation == null) {
            return null;
        }

        // 获取图片信息
        AnnotationImages image = annotationImagesService.selectAnnotationImagesByImageId(reviewTask.getImageId());

        // 获取该图片的所有标注列表
        AnnotationAnnotations queryAnnotation = new AnnotationAnnotations();
        queryAnnotation.setImageId(reviewTask.getImageId());
        queryAnnotation.setCategoryId(currentAnnotation.getCategoryId());
        List<AnnotationAnnotations> annotations = annotationAnnotationsService.selectAnnotationAnnotationsList(queryAnnotation);

        // 构建返回对象
        return ReviewTaskWithDetailsVo.builder()
                .reviewTask(reviewTask)
                .image(image)
                .annotations(annotations != null ? annotations : Collections.emptyList())
                .currentAnnotation(currentAnnotation)
                .auditor(reviewTask.getAuditor())
                .reviewStatus(reviewTask.getStatus() != null ? reviewTask.getStatus().name() : null)
                .build();
    }

    /**
     * 从审核任务中获取分类ID
     */
    private Long getCategoryIdFromTask(AnnotationReviews reviewTask) {
        if (reviewTask.getAnnotationId() == null) {
            return null;
        }

        AnnotationAnnotations annotation = annotationAnnotationsService.selectAnnotationAnnotationsByAnnotationId(reviewTask.getAnnotationId());
        return annotation != null ? annotation.getCategoryId() : null;
    }
}
