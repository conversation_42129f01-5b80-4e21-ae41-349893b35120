package com.ylzx.annotation.utils;

import com.ylzx.annotation.domain.AnnotationCategoriesImages;
import com.ylzx.annotation.service.AnnotationCategoriesImagesService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 标注员相关工具类
 * 
 * <AUTHOR>
 * @date 2025-07-25
 */
@Slf4j
@Component
public class AnnotatorHelper {

    @Autowired
    private AnnotationCategoriesImagesService annotationCategoriesImagesService;

    /**
     * 根据图片ID和分类ID查找标注员ID
     * 
     * @param imageId 图片ID
     * @param categoryId 分类ID
     * @return 标注员ID，如果未找到则返回null
     */
    public Long findAnnotatorId(Long imageId, Long categoryId) {
        if (imageId == null || categoryId == null) {
            log.debug("图片ID或分类ID为空，无法查找标注员ID");
            return null;
        }

        try {
            AnnotationCategoriesImages categoryImage = new AnnotationCategoriesImages();
            categoryImage.setImageId(imageId);
            categoryImage.setCategoryId(categoryId);

            List<AnnotationCategoriesImages> categoryImages = annotationCategoriesImagesService.selectAnnotationCategoriesImagesList(categoryImage);
            if (!categoryImages.isEmpty()) {
                Long annotatorId = categoryImages.get(0).getAnnotatorId();
                log.debug("找到标注员ID: {} (图片ID: {}, 分类ID: {})", annotatorId, imageId, categoryId);
                return annotatorId;
            } else {
                log.debug("未找到匹配的标注员记录 (图片ID: {}, 分类ID: {})", imageId, categoryId);
                return null;
            }
        } catch (Exception e) {
            log.error("查找标注员ID时发生异常 (图片ID: {}, 分类ID: {})", imageId, categoryId, e);
            return null;
        }
    }
}
