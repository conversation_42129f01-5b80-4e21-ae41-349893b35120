<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ylzx.annotation.mapper.AnnotationProjectConfigsMapper">
    
    <resultMap type="com.ylzx.annotation.domain.AnnotationProjectConfigs" id="AnnotationProjectConfigsResult">
        <result property="configId"    column="config_id"    />
        <result property="projectId"    column="project_id"    />
        <result property="width"    column="width"    />
        <result property="height"    column="height"    />
        <result property="transformations"    column="transformations"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
    </resultMap>

    <sql id="selectAnnotationProjectConfigsVo">
        select config_id, project_id, width, height, transformations, create_time, update_time, create_by, update_by from annotation_project_configs
    </sql>

    <select id="selectAnnotationProjectConfigsList" parameterType="AnnotationProjectConfigs" resultMap="AnnotationProjectConfigsResult">
        <include refid="selectAnnotationProjectConfigsVo"/>
        <where>  
            <if test="projectId != null "> and project_id = #{projectId}</if>
            <if test="width != null "> and width = #{width}</if>
            <if test="height != null "> and height = #{height}</if>
            <if test="transformations != null  and transformations != ''"> and transformations = #{transformations}</if>
        </where>
    </select>
    
    <select id="selectAnnotationProjectConfigsByConfigId" parameterType="Long" resultMap="AnnotationProjectConfigsResult">
        <include refid="selectAnnotationProjectConfigsVo"/>
        where config_id = #{configId}
    </select>

    <insert id="insertAnnotationProjectConfigs" parameterType="AnnotationProjectConfigs" useGeneratedKeys="true" keyProperty="configId">
        insert into annotation_project_configs
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="projectId != null">project_id,</if>
            <if test="width != null">width,</if>
            <if test="height != null">height,</if>
            <if test="transformations != null">transformations,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="projectId != null">#{projectId},</if>
            <if test="width != null">#{width},</if>
            <if test="height != null">#{height},</if>
            <if test="transformations != null">#{transformations},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
         </trim>
    </insert>

    <update id="updateAnnotationProjectConfigs" parameterType="AnnotationProjectConfigs">
        update annotation_project_configs
        <trim prefix="SET" suffixOverrides=",">
            <if test="projectId != null">project_id = #{projectId},</if>
            <if test="width != null">width = #{width},</if>
            <if test="height != null">height = #{height},</if>
            <if test="transformations != null">transformations = #{transformations},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
        </trim>
        where config_id = #{configId}
    </update>

    <delete id="deleteAnnotationProjectConfigsByConfigId" parameterType="Long">
        delete from annotation_project_configs where config_id = #{configId}
    </delete>

    <delete id="deleteAnnotationProjectConfigsByConfigIds" parameterType="String">
        delete from annotation_project_configs where config_id in 
        <foreach item="configId" collection="array" open="(" separator="," close=")">
            #{configId}
        </foreach>
    </delete>
</mapper>