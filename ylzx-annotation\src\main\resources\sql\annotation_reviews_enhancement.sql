-- =============================================
-- 标注审核表增强脚本
-- 添加 annotator_id 和 image_id 字段
-- 作者: qiud
-- 日期: 2025-06-24
-- =============================================

-- 1. 为 annotation_reviews 表添加新字段
ALTER TABLE annotation_reviews 
ADD COLUMN annotator_id BIGINT COMMENT '标注人ID',
ADD COLUMN image_id BIGINT COMMENT '图片ID';

-- 2. 添加索引以提高查询性能
CREATE INDEX idx_annotation_reviews_annotator_id ON annotation_reviews(annotator_id);
CREATE INDEX idx_annotation_reviews_image_id ON annotation_reviews(image_id);
CREATE INDEX idx_annotation_reviews_annotator_status ON annotation_reviews(annotator_id, status);
CREATE INDEX idx_annotation_reviews_image_status ON annotation_reviews(image_id, status);

-- 3. 添加外键约束（可选，根据实际需求决定是否启用）
-- ALTER TABLE annotation_reviews 
-- ADD CONSTRAINT fk_annotation_reviews_annotator 
-- FOREIGN KEY (annotator_id) REFERENCES sys_user(user_id);

-- ALTER TABLE annotation_reviews 
-- ADD CONSTRAINT fk_annotation_reviews_image 
-- FOREIGN KEY (image_id) REFERENCES annotation_images(image_id);

-- 4. 数据迁移脚本 - 为现有记录填充 annotator_id 和 image_id
-- 注意：这个脚本需要根据实际的数据关系进行调整

UPDATE annotation_reviews ar
SET 
    image_id = (
        SELECT aa.image_id 
        FROM annotation_annotations aa 
        WHERE aa.annotation_id = ar.annotation_id 
        LIMIT 1
    ),
    annotator_id = (
        SELECT aci.annotator_id 
        FROM annotation_annotations aa 
        JOIN annotation_categories_images aci 
            ON aa.image_id = aci.image_id 
            AND aa.category_id = aci.category_id
        WHERE aa.annotation_id = ar.annotation_id 
        LIMIT 1
    )
WHERE ar.image_id IS NULL OR ar.annotator_id IS NULL;

-- 5. 验证数据迁移结果
SELECT 
    COUNT(*) as total_reviews,
    COUNT(annotator_id) as reviews_with_annotator,
    COUNT(image_id) as reviews_with_image,
    COUNT(*) - COUNT(annotator_id) as missing_annotator,
    COUNT(*) - COUNT(image_id) as missing_image
FROM annotation_reviews;

-- 6. 创建视图以便于查询和统计
CREATE OR REPLACE VIEW v_annotation_review_stats AS
SELECT 
    ar.annotator_id,
    u.user_name as annotator_name,
    COUNT(*) as total_reviews,
    SUM(CASE WHEN ar.status = '3' THEN 1 ELSE 0 END) as approved_count,
    SUM(CASE WHEN ar.status = '4' THEN 1 ELSE 0 END) as rejected_count,
    SUM(CASE WHEN ar.status = '2' THEN 1 ELSE 0 END) as pending_count,
    ROUND(SUM(CASE WHEN ar.status = '3' THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2) as approval_rate,
    ROUND(SUM(CASE WHEN ar.status = '4' THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2) as rejection_rate
FROM annotation_reviews ar
LEFT JOIN sys_user u ON ar.annotator_id = u.user_id
WHERE ar.annotator_id IS NOT NULL
GROUP BY ar.annotator_id, u.user_name;

-- 7. 创建图片审核统计视图
CREATE OR REPLACE VIEW v_image_review_stats AS
SELECT 
    ar.image_id,
    ai.original_filename,
    COUNT(*) as review_count,
    SUM(CASE WHEN ar.status = '3' THEN 1 ELSE 0 END) as approved_count,
    SUM(CASE WHEN ar.status = '4' THEN 1 ELSE 0 END) as rejected_count,
    SUM(CASE WHEN ar.status = '2' THEN 1 ELSE 0 END) as pending_count,
    MAX(ar.audit_time) as last_review_time
FROM annotation_reviews ar
LEFT JOIN annotation_images ai ON ar.image_id = ai.image_id
WHERE ar.image_id IS NOT NULL
GROUP BY ar.image_id, ai.original_filename;

-- 8. 添加表注释
ALTER TABLE annotation_reviews COMMENT = '标注审核表 - 增强版，包含标注人ID和图片ID字段';

-- 9. 添加字段注释
ALTER TABLE annotation_reviews 
MODIFY COLUMN annotator_id BIGINT COMMENT '标注人ID，关联sys_user表的user_id',
MODIFY COLUMN image_id BIGINT COMMENT '图片ID，关联annotation_images表的image_id';

-- 10. 创建触发器以确保数据一致性（可选）
-- 当插入新的审核记录时，自动填充 annotator_id 和 image_id
DELIMITER $$

CREATE TRIGGER tr_annotation_reviews_before_insert
BEFORE INSERT ON annotation_reviews
FOR EACH ROW
BEGIN
    -- 如果 image_id 为空，从 annotation_annotations 表获取
    IF NEW.image_id IS NULL AND NEW.annotation_id IS NOT NULL THEN
        SELECT aa.image_id INTO NEW.image_id
        FROM annotation_annotations aa
        WHERE aa.annotation_id = NEW.annotation_id
        LIMIT 1;
    END IF;
    
    -- 如果 annotator_id 为空，从 annotation_categories_images 表获取
    IF NEW.annotator_id IS NULL AND NEW.image_id IS NOT NULL THEN
        SELECT aci.annotator_id INTO NEW.annotator_id
        FROM annotation_annotations aa
        JOIN annotation_categories_images aci 
            ON aa.image_id = aci.image_id 
            AND aa.category_id = aci.category_id
        WHERE aa.annotation_id = NEW.annotation_id
        LIMIT 1;
    END IF;
END$$

DELIMITER ;

-- 11. 示例查询语句
-- 查询某个标注人的审核统计
-- SELECT * FROM v_annotation_review_stats WHERE annotator_id = 1001;

-- 查询某张图片的审核历史
-- SELECT * FROM annotation_reviews WHERE image_id = 2001 ORDER BY audit_time DESC;

-- 查询审核通过率最高的标注人
-- SELECT * FROM v_annotation_review_stats ORDER BY approval_rate DESC LIMIT 10;

-- 查询需要重新审核的图片
-- SELECT * FROM v_image_review_stats WHERE rejected_count > 0;

COMMIT;
